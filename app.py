
from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, send_from_directory, current_app
from werkzeug.security import generate_password_hash, check_password_hash
from werkzeug.utils import secure_filename
import os
import qrcode
from datetime import datetime, timedelta
import uuid
import pandas as pd
import json
import logging
from logging.handlers import RotatingFileHandler
from config import Config

import pymysql
pymysql.install_as_MySQLdb()
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager
from flask_socketio import SocketIO, emit


# Import extensions
from extensions import db, login_manager, socketio

# Import Flask-Login components
from flask_login import login_required, current_user, login_user, logout_user
# SocketIO already imported above

# Admin decorator
from functools import wraps

def admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            return redirect(url_for('login'))
        if current_user.role != 'Administrator':
            flash('Access denied. Administrator privileges required.')
            return redirect(url_for('dashboard'))
        return f(*args, **kwargs)
    return decorated_function


# Initialize Flask app
app = Flask(__name__)
app.config.from_object(Config)

# Security headers middleware
@app.after_request
def add_security_headers(response):
    response.headers['X-Content-Type-Options'] = 'nosniff'
    response.headers['X-Frame-Options'] = 'DENY'
    response.headers['X-XSS-Protection'] = '1; mode=block'
    response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
    response.headers['Content-Security-Policy'] = "default-src 'self'; script-src 'self' 'unsafe-inline' cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' cdnjs.cloudflare.com; font-src 'self' cdnjs.cloudflare.com; img-src 'self' data:;"
    return response

# File upload validation
def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in app.config['ALLOWED_EXTENSIONS']

# Ensure upload directories exist
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs(app.config['QR_CODE_FOLDER'], exist_ok=True)

# Initialize extensions with app
db.init_app(app)
login_manager.init_app(app)
socketio.init_app(app)

# Setup logging
if not app.debug:
    if not os.path.exists('logs'):
        os.mkdir('logs')
    file_handler = RotatingFileHandler('logs/toffice.log', maxBytes=10240000, backupCount=10)
    file_handler.setFormatter(logging.Formatter(
        '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
    ))
    file_handler.setLevel(logging.INFO)
    app.logger.addHandler(file_handler)
    app.logger.setLevel(logging.INFO)
    app.logger.info('T-Office startup')

# Initialize CLI commands
import commands
commands.init_app(app)

# Import models
from models.user import User
from models.file import File, CompartmentQR
from models.location import Location
from models.access_log import AccessLog
from models.bulk_data_security import BulkDataAccessSession, BulkDataAccessLog, DataEncryption
from models.bundle import Bundle, BundleAssignment, BundleStatistics

# Import security utilities
from utils.security import (
    bulk_data_access_required, secure_search_required,
    validate_search_criteria, create_bulk_access_session,
    get_active_bulk_session, filter_bulk_data_results,
    log_bulk_data_access, check_export_permissions
)

# Import bundle utilities
from utils.bundle_manager import BundleManager

# Add custom Jinja2 filters
@app.template_filter('from_json')
def from_json_filter(value):
    """Parse JSON string to Python object"""
    if not value:
        return {}
    try:
        return json.loads(value)
    except (json.JSONDecodeError, TypeError):
        return {}

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# Error handlers
@app.errorhandler(404)
def not_found_error(error):
    return render_template('404.html'), 404

@app.errorhandler(500)
def internal_error(error):
    db.session.rollback()
    return render_template('500.html'), 500

@app.errorhandler(413)
def too_large(error):
    flash('File too large. Maximum size is 32MB.', 'error')
    return redirect(request.url)

@app.errorhandler(403)
def forbidden(error):
    flash('Access denied. Insufficient permissions.', 'error')
    return redirect(url_for('dashboard'))

# Routes
@app.route('/')
def index():
    return render_template('base.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')

        user = User.query.filter_by(username=username).first()

        if user and check_password_hash(user.password_hash, password):
            login_user(user)
            return redirect(url_for('dashboard'))

        flash('Invalid username or password')

    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    logout_user()
    return redirect(url_for('index'))

@app.route('/dashboard')
@login_required
def dashboard():
    files = File.query.filter_by(user_id=current_user.id).all()

    # Calculate stats for dashboard
    total_files = len(files)
    files_with_access_logs = len([f for f in files if f.access_logs.count() > 0])
    files_with_qr_codes = len([f for f in files if f.qr_code])
    compartment_qrs = CompartmentQR.query.count()

    stats = {
        'total_files': total_files,
        'processed_files': files_with_access_logs,
        'qr_codes_generated': files_with_qr_codes + compartment_qrs,
        'compartment_qrs': compartment_qrs
    }

    return render_template('dashboard.html', files=files, stats=stats)

@app.route('/files/add', methods=['GET', 'POST'])
@login_required
def add_file():
    if request.method == 'POST':
        # Handle file upload
        if 'file' not in request.files:
            flash('No file part')
            return redirect(request.url)

        file = request.files['file']

        if file.filename == '':
            flash('No selected file')
            return redirect(request.url)

        if file:
            filename = secure_filename(file.filename)

            # Validate file type
            if not allowed_file(filename):
                flash('File type not allowed. Allowed types: txt, pdf, png, jpg, jpeg, gif, doc, docx, xls, xlsx', 'error')
                return redirect(request.url)

            # Check if it's an Excel file
            if filename.endswith(('.xlsx', '.xls')):
                try:
                    # Process Excel file to extract data
                    df = pd.read_excel(file)

                    # Expected columns for Excel processing
                    required_columns = ['IndexID', 'RefID', 'FILE_NO', 'Category', 'Year', 'DisposalCat',
                                      'Createddate', 'RowNo', 'RackNo', 'RecordRoomSlNo', 'BundleNo',
                                      'ClosureDate', 'ReceiptAtRRDate', 'DestructionDate', 'Subject',
                                      'dist_name_en', 'taluk_name_en', 'hobli_name_en', 'village_name_en', 'survey_no']

                    # Check if Excel has the required columns
                    missing_columns = [col for col in required_columns if col not in df.columns]
                    if missing_columns:
                        flash(f'Excel file missing required columns: {", ".join(missing_columns)}')
                        return redirect(request.url)

                    # Process first row of Excel data (or allow user to select row)
                    row_index = int(request.form.get('excel_row_index', 0))
                    if row_index >= len(df):
                        flash('Invalid row index selected')
                        return redirect(request.url)

                    row = df.iloc[row_index]

                    # Extract data from Excel row
                    rack_number = str(row['RackNo']) if pd.notna(row['RackNo']) else '1'
                    row_number = str(row['RowNo']) if pd.notna(row['RowNo']) else '1'
                    position = str(row['RecordRoomSlNo']) if pd.notna(row['RecordRoomSlNo']) else '1'
                    file_no = str(row['FILE_NO']) if pd.notna(row['FILE_NO']) else f'FILE_{row_index+1}'

                    # Create title from Excel data
                    title_parts = []
                    if pd.notna(row['FILE_NO']):
                        title_parts.append(f"File: {row['FILE_NO']}")
                    if pd.notna(row['Subject']):
                        title_parts.append(str(row['Subject']))
                    if pd.notna(row['village_name_en']):
                        title_parts.append(f"Village: {row['village_name_en']}")

                    title = ' - '.join(title_parts) if title_parts else f'Excel Import {row_index+1}'

                    # Create description from Excel data
                    description_parts = []
                    if pd.notna(row['RefID']):
                        description_parts.append(f"RefID: {row['RefID']}")
                    if pd.notna(row['Category']):
                        description_parts.append(f"Category: {row['Category']}")
                    if pd.notna(row['Year']):
                        description_parts.append(f"Year: {row['Year']}")
                    if pd.notna(row['dist_name_en']):
                        description_parts.append(f"District: {row['dist_name_en']}")
                    if pd.notna(row['taluk_name_en']):
                        description_parts.append(f"Taluk: {row['taluk_name_en']}")
                    if pd.notna(row['hobli_name_en']):
                        description_parts.append(f"Hobli: {row['hobli_name_en']}")
                    if pd.notna(row['village_name_en']):
                        description_parts.append(f"Village: {row['village_name_en']}")
                    if pd.notna(row['survey_no']):
                        description_parts.append(f"Survey No: {row['survey_no']}")

                    description = ' | '.join(description_parts) if description_parts else 'Imported from Excel'

                except Exception as e:
                    flash(f'Error processing Excel file: {str(e)}')
                    return redirect(request.url)
            else:
                # For non-Excel files, use form data
                title = request.form.get('title')
                description = request.form.get('description')
                rack_number = request.form.get('rack_number')
                row_number = request.form.get('row_number')
                position = request.form.get('position')

                if not all([title, rack_number, row_number, position]):
                    flash('Please fill in all required fields for non-Excel files')
                    return redirect(request.url)

            # Save the uploaded file
            unique_filename = f"{uuid.uuid4()}_{filename}"
            file_path = os.path.join(app.config['UPLOAD_FOLDER'], unique_filename)
            file.save(file_path)

            # Create location record
            location = Location(rack_number=rack_number, row_number=row_number, position=position)
            db.session.add(location)
            db.session.flush()  # Get the location ID without committing

            # Create file record
            new_file = File(
                title=title,
                description=description,
                filename=unique_filename,
                original_filename=filename,
                location_id=location.id,
                user_id=current_user.id
            )
            db.session.add(new_file)
            db.session.flush()  # Get the file ID without committing

            # Generate QR code
            qr_data = {
                'file_id': new_file.id,
                'title': title,
                'location': f"Rack: {rack_number}, Row: {row_number}, Position: {position}",
                'url': f"http://127.0.0.1:5001/files/{new_file.id}",
                'type': 'T-Office-File'
            }

            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_M,
                box_size=10,
                border=4,
            )

            qr.add_data(json.dumps(qr_data))
            qr.make(fit=True)

            qr_img = qr.make_image(fill_color="black", back_color="white")
            qr_filename = f"qr_{new_file.id}.png"
            qr_path = os.path.join(app.config['QR_CODE_FOLDER'], qr_filename)
            qr_img.save(qr_path)

            # Update file with QR code path
            new_file.qr_code = qr_filename

            # Log the file access
            access_log = AccessLog(
                file_id=new_file.id,
                user_id=current_user.id,
                action="created"
            )
            db.session.add(access_log)

            # Commit all changes
            db.session.commit()

            flash('File added successfully')
            return redirect(url_for('view_file', file_id=new_file.id))

    return render_template('add_file.html')

@app.route('/files/<int:file_id>')
@login_required
def view_file(file_id):
    file = File.query.get_or_404(file_id)

    # Check if this is bulk uploaded data and apply security
    is_bulk_data = bool(file.import_batch_id)
    access_denied = False
    denial_reason = ""

    if is_bulk_data and current_user.role != 'Administrator':
        # Check for active bulk access session
        active_session = get_active_bulk_session(current_user.id)
        if not active_session:
            access_denied = True
            denial_reason = "Bulk data access requires additional verification"

            # Log denied access attempt
            from utils.security import get_client_info
            client_info = get_client_info()
            access_log = AccessLog(
                file_id=file.id,
                user_id=current_user.id,
                action="view_denied",
                is_bulk_data=True,
                access_granted=False,
                denial_reason=denial_reason,
                ip_address=client_info['ip_address'],
                user_agent=client_info['user_agent']
            )
            db.session.add(access_log)
            db.session.commit()

            flash(f'Access denied: {denial_reason}. Please complete bulk data verification.', 'error')
            return redirect(url_for('bulk_data_unlock'))

    # Process excel_row_data based on user role
    excel_data = None
    if file.excel_row_data:
        try:
            excel_data = json.loads(file.excel_row_data)
            if is_bulk_data and current_user.role != 'Administrator':
                excel_data = DataEncryption.mask_sensitive_data(excel_data)
        except json.JSONDecodeError:
            excel_data = None

    # Log the file access
    session_id = None
    if is_bulk_data and current_user.role != 'Administrator':
        active_session = get_active_bulk_session(current_user.id)
        if active_session:
            session_id = active_session.id
            active_session.record_activity()

    # Enhanced logging for bulk data
    try:
        from utils.security import get_client_info
        client_info = get_client_info()

        # Create access log with error handling for missing columns
        access_log_data = {
            'file_id': file.id,
            'user_id': current_user.id,
            'action': "viewed"
        }

        # Add security fields if they exist in the database
        try:
            access_log_data.update({
                'is_bulk_data': is_bulk_data,
                'access_session_id': session_id,
                'ip_address': client_info['ip_address'],
                'user_agent': client_info['user_agent']
            })
        except Exception:
            # If security columns don't exist, just log basic info
            pass

        access_log = AccessLog(**access_log_data)
        db.session.add(access_log)
        db.session.commit()

    except Exception as e:
        # Fallback to basic logging if enhanced logging fails
        try:
            basic_log = AccessLog(
                file_id=file.id,
                user_id=current_user.id,
                action="viewed"
            )
            db.session.add(basic_log)
            db.session.commit()
        except Exception:
            # If even basic logging fails, continue without logging
            pass

    # Log bulk data specific access
    if is_bulk_data:
        log_bulk_data_access(file_id, 'viewed', session_id)

    # Get access logs for this file
    access_logs = AccessLog.query.filter_by(file_id=file.id).order_by(AccessLog.timestamp.desc()).limit(10).all()

    return render_template('view_file.html',
                         file=file,
                         access_logs=access_logs,
                         is_bulk_data=is_bulk_data,
                         excel_data=excel_data,
                         user_role=current_user.role)

@app.route('/files/<int:file_id>/download')
@login_required
def download_file(file_id):
    file = File.query.get_or_404(file_id)

    # Check if file has actual filename (not Excel import)
    if not file.filename:
        flash('This file was imported from Excel and has no downloadable file')
        return redirect(url_for('view_file', file_id=file_id))

    # Check export permissions for bulk data
    can_export, export_message = check_export_permissions(file_id, current_user.role)
    if not can_export:
        flash(f'Download denied: {export_message}', 'error')
        return redirect(url_for('view_file', file_id=file_id))

    # Check bulk data access session if needed
    is_bulk_data = bool(file.import_batch_id)
    session_id = None

    if is_bulk_data and current_user.role != 'Administrator':
        active_session = get_active_bulk_session(current_user.id)
        if not active_session:
            flash('Bulk data download requires active verification session', 'error')
            return redirect(url_for('bulk_data_unlock'))
        session_id = active_session.id
        active_session.record_activity()

    # Enhanced logging for downloads
    try:
        from utils.security import get_client_info
        client_info = get_client_info()

        # Create access log with error handling for missing columns
        access_log_data = {
            'file_id': file.id,
            'user_id': current_user.id,
            'action': "downloaded"
        }

        # Add security fields if they exist in the database
        try:
            access_log_data.update({
                'is_bulk_data': is_bulk_data,
                'access_session_id': session_id,
                'ip_address': client_info['ip_address'],
                'user_agent': client_info['user_agent']
            })
        except Exception:
            # If security columns don't exist, just log basic info
            pass

        access_log = AccessLog(**access_log_data)
        db.session.add(access_log)
        db.session.commit()

    except Exception as e:
        # Fallback to basic logging if enhanced logging fails
        try:
            basic_log = AccessLog(
                file_id=file.id,
                user_id=current_user.id,
                action="downloaded"
            )
            db.session.add(basic_log)
            db.session.commit()
        except Exception:
            # If even basic logging fails, continue without logging
            pass

    # Log bulk data specific access
    if is_bulk_data:
        log_bulk_data_access(file_id, 'downloaded', session_id)

    return send_from_directory(
        app.config['UPLOAD_FOLDER'],
        file.filename,
        as_attachment=True,
        attachment_filename=file.original_filename or file.filename
    )

@app.route('/files/<int:file_id>/qrcode')
@login_required
def get_qrcode(file_id):
    file = File.query.get_or_404(file_id)

    # Check if QR code exists
    if not file.qr_code:
        flash('QR code not found for this file')
        return redirect(url_for('view_file', file_id=file_id))

    return send_from_directory(
        app.config['QR_CODE_FOLDER'],
        file.qr_code,
        as_attachment=False
    )

@app.route('/scan', methods=['GET', 'POST'])
@login_required
def scan_qrcode():
    if request.method == 'POST':
        # Handle QR code data processing
        qr_data = request.form.get('qr_data')
        file_id = request.form.get('file_id')

        if qr_data:
            try:
                # Parse QR code JSON data
                qr_json = json.loads(qr_data)
                qr_type = qr_json.get('type')

                # Handle compartment QR codes
                if qr_type == 'T-Office-Compartment':
                    # Redirect to compartment search interface
                    return redirect(url_for('compartment_search'))

                # Handle individual file QR codes
                elif qr_type == 'T-Office-File':
                    file_id = qr_json.get('file_id')
                    if file_id:
                        return redirect(url_for('view_file', file_id=file_id))

            except json.JSONDecodeError:
                flash('Invalid QR code data format')

        # Fallback for legacy file_id parameter
        elif file_id:
            return redirect(url_for('view_file', file_id=file_id))

        flash('No valid QR code data found')

    return render_template('scan.html')

@app.route('/api/scan', methods=['POST'])
def api_scan_qrcode():
    data = request.json
    qr_data = data.get('qr_data')

    if not qr_data:
        return jsonify({'success': False, 'error': 'No QR data provided'})

    try:
        # First check if this is a compartment QR code
        try:
            qr_dict = json.loads(qr_data)
            if qr_dict.get('type') == 'T-Office-Compartment':
                # This is a compartment QR code, should be handled by the frontend redirect
                return jsonify({
                    'success': False,
                    'error': 'Compartment QR codes should be handled by frontend redirect',
                    'compartment_data': qr_dict
                })
        except:
            pass

        file_id = None

        # Try multiple parsing methods for different QR code formats

        # Method 1: Try parsing as JSON
        try:
            qr_dict = json.loads(qr_data)
            file_id = qr_dict.get('file_id')
        except:
            pass

        # Method 2: Try parsing as Python literal (ast.literal_eval)
        if not file_id:
            try:
                import ast
                qr_dict = ast.literal_eval(qr_data)
                file_id = qr_dict.get('file_id')
            except:
                pass

        # Method 3: Try parsing as URL (e.g., http://domain.com/files/123)
        if not file_id:
            try:
                import re
                # Look for patterns like /files/123 or file_id=123
                url_match = re.search(r'/files/(\d+)', qr_data)
                if url_match:
                    file_id = int(url_match.group(1))
                else:
                    # Look for file_id parameter
                    id_match = re.search(r'file_id[=:](\d+)', qr_data)
                    if id_match:
                        file_id = int(id_match.group(1))
            except:
                pass

        # Method 4: Try parsing as plain number (direct file ID)
        if not file_id:
            try:
                file_id = int(qr_data.strip())
            except:
                pass

        # Method 5: Try extracting from T-Office specific format
        if not file_id:
            try:
                # Look for T-Office format: "T-Office-File-ID-123"
                if 'T-Office' in qr_data:
                    id_match = re.search(r'ID[:-](\d+)', qr_data)
                    if id_match:
                        file_id = int(id_match.group(1))
            except:
                pass

        if file_id:
            file = File.query.get(file_id)
            if file:
                # Log the file access
                access_log = AccessLog(
                    file_id=file.id,
                    user_id=current_user.id if current_user.is_authenticated else None,
                    action="scanned"
                )
                db.session.add(access_log)
                db.session.commit()

                return jsonify({
                    'success': True,
                    'file_id': file.id,
                    'title': file.title,
                    'location': {
                        'rack': file.location.rack_number,
                        'row': file.location.row_number,
                        'position': file.location.position
                    }
                })
            else:
                return jsonify({'success': False, 'error': f'File with ID {file_id} not found'})
        else:
            return jsonify({'success': False, 'error': f'Could not extract file ID from QR data: {qr_data[:100]}...'})

    except Exception as e:
        return jsonify({'success': False, 'error': f'Error processing QR code: {str(e)}'})

@app.route('/api/scan/debug', methods=['POST'])
def api_scan_debug():
    """Debug endpoint to help troubleshoot QR scanning issues"""
    data = request.json
    qr_data = data.get('qr_data')

    debug_info = {
        'received_data': qr_data,
        'data_type': type(qr_data).__name__,
        'data_length': len(qr_data) if qr_data else 0,
        'parsing_attempts': []
    }

    if not qr_data:
        debug_info['error'] = 'No QR data provided'
        return jsonify(debug_info)

    file_id = None

    # Method 1: Try parsing as JSON
    try:
        import json
        qr_dict = json.loads(qr_data)
        file_id = qr_dict.get('file_id')
        debug_info['parsing_attempts'].append({
            'method': 'JSON parsing',
            'success': bool(file_id),
            'result': qr_dict if file_id else 'No file_id found'
        })
    except Exception as e:
        debug_info['parsing_attempts'].append({
            'method': 'JSON parsing',
            'success': False,
            'error': str(e)
        })

    # Method 2: Try parsing as Python literal
    if not file_id:
        try:
            import ast
            qr_dict = ast.literal_eval(qr_data)
            file_id = qr_dict.get('file_id')
            debug_info['parsing_attempts'].append({
                'method': 'Python literal parsing',
                'success': bool(file_id),
                'result': qr_dict if file_id else 'No file_id found'
            })
        except Exception as e:
            debug_info['parsing_attempts'].append({
                'method': 'Python literal parsing',
                'success': False,
                'error': str(e)
            })

    # Add more debug info
    debug_info['final_file_id'] = file_id
    debug_info['success'] = bool(file_id)

    return jsonify(debug_info)

@app.route('/api/analytics', methods=['POST'])
def api_analytics():
    """Handle analytics events from the frontend"""
    try:
        data = request.json
        events = data.get('events', [])

        # Log analytics events (you can store these in database if needed)
        for event in events:
            print(f"Analytics Event: {event.get('name')} - {event.get('data')}")

        return jsonify({'success': True, 'processed': len(events)})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/search', methods=['GET'])
@login_required
def api_search():
    """Search files by title or description"""
    try:
        query = request.args.get('q', '').strip()
        if len(query) < 2:
            return jsonify({'results': []})

        # Search files by title or description
        files = File.query.filter(
            db.or_(
                File.title.contains(query),
                File.description.contains(query)
            )
        ).limit(10).all()

        results = []
        for file in files:
            results.append({
                'id': file.id,
                'title': file.title,
                'description': file.description,
                'location': {
                    'rack': file.location.rack_number,
                    'row': file.location.row_number,
                    'position': file.location.position
                }
            })

        return jsonify({'results': results})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/upload_excel', methods=['GET', 'POST'])
@login_required
def upload_excel():
    """Upload and process Excel file to automatically create file records"""
    if request.method == 'POST':
        if 'excel_file' not in request.files:
            flash('No Excel file selected')
            return redirect(request.url)

        excel_file = request.files['excel_file']

        if excel_file.filename == '':
            flash('No file selected')
            return redirect(request.url)

        if excel_file and excel_file.filename.endswith(('.xlsx', '.xls')):
            try:
                # Read Excel file
                df = pd.read_excel(excel_file)

                # Expected columns mapping
                column_mapping = {
                    'IndexID': 'index_id',
                    'RefID': 'ref_id',
                    'FILE_NO': 'file_no',
                    'Category': 'category',
                    'Year': 'year',
                    'DisposalCat': 'disposal_cat',
                    'Createddate': 'created_date',
                    'RowNo': 'row_no',
                    'RackNo': 'rack_no',
                    'RecordRoomSlNo': 'record_room_sl_no',
                    'BundleNo': 'bundle_no',
                    'ClosureDate': 'closure_date',
                    'ReceiptAtRRDate': 'receipt_at_rr_date',
                    'DestructionDate': 'destruction_date',
                    'Subject': 'subject',
                    'dist_name_en': 'district_name',
                    'taluk_name_en': 'taluk_name',
                    'hobli_name_en': 'hobli_name',
                    'village_name_en': 'village_name',
                    'survey_no': 'survey_no'
                }

                # Check if required columns exist
                missing_columns = [col for col in column_mapping.keys() if col not in df.columns]
                if missing_columns:
                    flash(f'Missing required columns: {", ".join(missing_columns)}')
                    return redirect(request.url)

                created_files = 0
                errors = []

                for index, row in df.iterrows():
                    try:
                        # Extract data from Excel row
                        rack_no = str(row['RackNo']) if pd.notna(row['RackNo']) else '1'
                        row_no = str(row['RowNo']) if pd.notna(row['RowNo']) else '1'
                        file_no = str(row['FILE_NO']) if pd.notna(row['FILE_NO']) else f'FILE_{index+1}'
                        subject = str(row['Subject']) if pd.notna(row['Subject']) else 'No Subject'

                        # Create title from available data
                        title_parts = []
                        if pd.notna(row['FILE_NO']):
                            title_parts.append(f"File: {row['FILE_NO']}")
                        if pd.notna(row['Subject']):
                            title_parts.append(str(row['Subject']))
                        if pd.notna(row['village_name_en']):
                            title_parts.append(f"Village: {row['village_name_en']}")

                        title = ' - '.join(title_parts) if title_parts else f'Excel Import {index+1}'

                        # Create description from additional fields
                        description_parts = []
                        if pd.notna(row['RefID']):
                            description_parts.append(f"RefID: {row['RefID']}")
                        if pd.notna(row['Category']):
                            description_parts.append(f"Category: {row['Category']}")
                        if pd.notna(row['Year']):
                            description_parts.append(f"Year: {row['Year']}")
                        if pd.notna(row['dist_name_en']):
                            description_parts.append(f"District: {row['dist_name_en']}")
                        if pd.notna(row['taluk_name_en']):
                            description_parts.append(f"Taluk: {row['taluk_name_en']}")
                        if pd.notna(row['hobli_name_en']):
                            description_parts.append(f"Hobli: {row['hobli_name_en']}")
                        if pd.notna(row['village_name_en']):
                            description_parts.append(f"Village: {row['village_name_en']}")
                        if pd.notna(row['survey_no']):
                            description_parts.append(f"Survey No: {row['survey_no']}")

                        description = ' | '.join(description_parts) if description_parts else 'Imported from Excel'

                        # Create location record
                        location = Location(
                            rack_number=rack_no,
                            row_number=row_no,
                            position=str(row['RecordRoomSlNo']) if pd.notna(row['RecordRoomSlNo']) else '1'
                        )
                        db.session.add(location)
                        db.session.flush()

                        # Create file record
                        new_file = File(
                            title=title,
                            description=description,
                            filename=None,  # No physical file uploaded
                            original_filename=None,
                            location_id=location.id,
                            user_id=current_user.id
                        )
                        db.session.add(new_file)
                        db.session.flush()

                        # Generate QR code
                        qr_data = {
                            'file_id': new_file.id,
                            'title': title,
                            'file_no': file_no,
                            'location': f"Rack: {rack_no}, Row: {row_no}",
                            'url': f"http://127.0.0.1:5001/files/{new_file.id}",
                            'type': 'T-Office-File'
                        }

                        qr = qrcode.QRCode(
                            version=1,
                            error_correction=qrcode.constants.ERROR_CORRECT_M,
                            box_size=10,
                            border=4,
                        )

                        qr.add_data(json.dumps(qr_data))
                        qr.make(fit=True)

                        qr_img = qr.make_image(fill_color="black", back_color="white")
                        qr_filename = f"qr_{new_file.id}.png"
                        qr_path = os.path.join(app.config['QR_CODE_FOLDER'], qr_filename)
                        qr_img.save(qr_path)

                        # Update file with QR code path
                        new_file.qr_code = qr_filename

                        # Log the file creation
                        access_log = AccessLog(
                            file_id=new_file.id,
                            user_id=current_user.id,
                            action="created_from_excel"
                        )
                        db.session.add(access_log)

                        created_files += 1

                    except Exception as e:
                        # Rollback the current transaction for this row
                        db.session.rollback()
                        errors.append(f"Row {index+1}: {str(e)}")
                        continue

                # Commit all changes
                try:
                    db.session.commit()
                except Exception as e:
                    db.session.rollback()
                    flash(f'Database error: {str(e)}')
                    return redirect(request.url)

                if created_files > 0:
                    flash(f'Successfully created {created_files} file records from Excel')
                if errors:
                    flash(f'Errors encountered: {"; ".join(errors[:5])}')  # Show first 5 errors

                return redirect(url_for('dashboard'))

            except Exception as e:
                flash(f'Error processing Excel file: {str(e)}')
                return redirect(request.url)
        else:
            flash('Please upload a valid Excel file (.xlsx or .xls)')
            return redirect(request.url)

    return render_template('upload_excel.html')

# Data Preservation Helper Functions
def preserve_excel_row_data(row, row_index):
    """Preserve complete Excel row data as JSON for 100% data fidelity"""
    try:
        # Convert pandas Series to dict, handling all data types
        row_dict = {}
        for key, value in row.items():
            # Convert all values to string to preserve exact Excel content
            if pd.isna(value) or value == '':
                row_dict[key] = ''
            else:
                row_dict[key] = str(value)

        row_dict['_original_row_index'] = row_index
        return json.dumps(row_dict, ensure_ascii=False)
    except Exception as e:
        # Even if JSON serialization fails, preserve as string
        return f"Row {row_index}: {str(row.to_dict())}"

def safe_extract_field(row, field_name, default=''):
    """Safely extract field value without any data loss"""
    try:
        value = row.get(field_name, default)
        if pd.isna(value) or value == '':
            return default
        return str(value)  # Preserve as string to avoid data type conversion issues
    except:
        return default

def create_data_quality_assessment(row, row_index):
    """Assess data quality without blocking import"""
    warnings = []
    quality_score = 100

    # Check for common issues but don't block import
    ref_id = safe_extract_field(row, 'RefID')
    file_no = safe_extract_field(row, 'FILE_NO')
    rack_no = safe_extract_field(row, 'RackNo')

    # RefID quality check
    if not ref_id:
        warnings.append("Missing RefID")
        quality_score -= 10

    # File number quality check
    if not file_no:
        warnings.append("Missing FILE_NO")
        quality_score -= 10

    # Rack number quality check
    if rack_no:
        try:
            rack_num = int(rack_no)
            if rack_num < 1 or rack_num > 10000:  # Expanded range, no blocking
                warnings.append(f"RackNo {rack_no} outside typical range (1-10000)")
                quality_score -= 5
        except ValueError:
            warnings.append(f"RackNo '{rack_no}' is not numeric")
            quality_score -= 5
    else:
        warnings.append("Missing RackNo")
        quality_score -= 10

    # Location data quality check
    hobli = safe_extract_field(row, 'hobli_name')
    village = safe_extract_field(row, 'village_name')
    survey = safe_extract_field(row, 'survey_no')

    if not hobli and not village and not survey:
        warnings.append("Missing location data (hobli, village, survey)")
        quality_score -= 15

    return warnings, max(0, quality_score)

def create_comprehensive_title(row, row_index):
    """Create title without any length restrictions"""
    title_parts = []

    # Include all meaningful fields in title
    fields_for_title = ['FILE_NO', 'Subject', 'RefID', 'Category', 'hobli_name', 'village_name']

    for field in fields_for_title:
        value = safe_extract_field(row, field)
        if value:
            if field == 'Subject':
                title_parts.append(value)  # No truncation
            else:
                title_parts.append(f"{field}: {value}")

    if not title_parts:
        title_parts.append(f"Excel Import Row {row_index}")

    return ' | '.join(title_parts)

def create_comprehensive_description(row):
    """Create searchable description with all Excel fields"""
    description_parts = []

    # Include ALL Excel columns in description for complete searchability
    excel_columns = [
        'SL No', 'INDEX ID', 'RefID', 'FILE_NO', 'Category', 'DisposalCat',
        'Createddate', 'RowNo', 'RackNo', 'RecordRoomSlNo', 'BundleNo',
        'Subject', 'hobli_name', 'village_name', 'survey_no'
    ]

    for column in excel_columns:
        value = safe_extract_field(row, column)
        if value:
            description_parts.append(f"{column}: {value}")

    return ' | '.join(description_parts) if description_parts else 'Excel Import'

def safe_create_location(row, warnings_list):
    """Create location record with fallback values, never fail"""
    try:
        rack_no = safe_extract_field(row, 'RackNo', '1')
        row_no = safe_extract_field(row, 'RowNo', '1')
        position = safe_extract_field(row, 'RecordRoomSlNo', '1')

        # Clean up values but don't validate strictly
        if not rack_no or rack_no == '':
            rack_no = '1'
            warnings_list.append("RackNo empty, defaulted to '1'")

        if not row_no or row_no == '':
            row_no = '1'
            warnings_list.append("RowNo empty, defaulted to '1'")

        if not position or position == '':
            position = '1'
            warnings_list.append("RecordRoomSlNo empty, defaulted to '1'")

        return Location(
            rack_number=str(rack_no),
            row_number=str(row_no),
            position=str(position)
        )
    except Exception as e:
        warnings_list.append(f"Location creation error: {str(e)}, using defaults")
        return Location(rack_number='1', row_number='1', position='1')

@app.route('/admin/bulk-upload', methods=['GET', 'POST'])
@admin_required
def admin_bulk_upload():
    """Admin interface for bulk Excel upload with 100% data preservation"""
    if request.method == 'POST':
        if 'excel_file' not in request.files:
            flash('No Excel file selected', 'error')
            return redirect(request.url)

        excel_file = request.files['excel_file']

        if excel_file.filename == '':
            flash('No file selected', 'error')
            return redirect(request.url)

        if not excel_file.filename.endswith(('.xlsx', '.xls')):
            flash('Please upload a valid Excel file (.xlsx or .xls)', 'error')
            return redirect(request.url)

        try:
            # Read Excel file with maximum compatibility - preserve all data
            df = pd.read_excel(excel_file, dtype=str, na_filter=False, keep_default_na=False)

            # NO FILE SIZE LIMITS - Import everything
            total_rows = len(df)

            # Generate unique batch ID for tracking
            import uuid
            batch_id = str(uuid.uuid4())[:8]

            # Define expected columns but don't require them - adapt to any Excel structure
            expected_columns = [
                'SL No', 'INDEX ID', 'RefID', 'FILE_NO', 'Category', 'DisposalCat',
                'Createddate', 'RowNo', 'RackNo', 'RecordRoomSlNo', 'BundleNo',
                'Subject', 'hobli_name', 'village_name', 'survey_no'
            ]

            # Identify available columns - work with whatever is provided
            available_columns = list(df.columns)
            missing_columns = [col for col in expected_columns if col not in available_columns]

            # Log missing columns but continue processing
            column_warnings = []
            if missing_columns:
                column_warnings.append(f"Missing expected columns: {', '.join(missing_columns)}")

            # Add missing columns with empty values to ensure compatibility
            for col in missing_columns:
                df[col] = ''

            # Handle completely empty file gracefully
            if total_rows == 0:
                flash('Excel file is empty but will be processed.', 'warning')
                return render_template('admin_bulk_upload.html', summary={
                    'total_rows': 0, 'created_count': 0, 'duplicate_count': 0,
                    'error_count': 0, 'warnings_count': 0, 'errors': [], 'warnings': ['File was empty']
                })

            # Initialize processing counters for 100% import success
            created_count = 0
            warnings_count = 0
            all_warnings = []
            all_warnings.extend(column_warnings)

            # Initialize compartment assignment tracking
            compartment_stats = {'compartment_1': 0, 'compartment_2': 0, 'unassigned': 0}

            # Process ALL rows - no skipping, no batch failures
            # Use smaller batches but with individual row isolation
            batch_size = 50  # Smaller batches for better error isolation

            for batch_start in range(0, total_rows, batch_size):
                batch_end = min(batch_start + batch_size, total_rows)
                batch_df = df.iloc[batch_start:batch_end]

                # Process each row individually with complete error isolation
                for index, row in batch_df.iterrows():
                    row_warnings = []

                    try:
                        # PRESERVE COMPLETE ROW DATA FIRST - This ensures 100% data preservation
                        excel_row_json = preserve_excel_row_data(row, index + 1)

                        # Assess data quality without blocking import
                        quality_warnings, quality_score = create_data_quality_assessment(row, index + 1)
                        row_warnings.extend(quality_warnings)

                        # Create title without length restrictions
                        title = create_comprehensive_title(row, index + 1)

                        # Create comprehensive searchable description
                        description = create_comprehensive_description(row)

                        # Create location with fallback values - never fail
                        location = safe_create_location(row, row_warnings)

                        # Start individual row transaction
                        try:
                            db.session.add(location)
                            db.session.flush()  # Get location ID

                            # Create file record with complete data preservation
                            new_file = File(
                                title=title,
                                description=description,
                                filename=None,  # No physical file for Excel imports
                                original_filename=None,
                                excel_row_data=excel_row_json,  # Complete row preservation
                                import_warnings=json.dumps(row_warnings) if row_warnings else None,
                                data_quality_score=quality_score,
                                import_batch_id=batch_id,
                                original_row_number=index + 1,
                                location_id=location.id,
                                user_id=current_user.id
                            )

                            db.session.add(new_file)
                            db.session.flush()  # Get file ID for QR code

                            # ENHANCED AUTOMATIC COMPARTMENT ASSIGNMENT BASED ON BUNDLE NUMBER
                            compartment_assignment_success = False
                            try:
                                bundle_no = safe_extract_field(row, 'BundleNo')
                                if bundle_no:
                                    try:
                                        bundle_number = int(float(str(bundle_no)))
                                        from utils.bundle_manager import BundleManager

                                        # Enhanced compartment assignment with detailed feedback
                                        compartment_info, assignment_error = BundleManager.assign_file_to_compartment_by_bundle(
                                            new_file.id, bundle_number, row_warnings
                                        )

                                        if compartment_info:
                                            compartment_assignment_success = True
                                            compartment_num = compartment_info['compartment_number']

                                            # Add detailed success message with navigation info
                                            success_msg = f"✅ Bundle {bundle_number} → Compartment {compartment_num}"
                                            navigation_msg = f"📍 Access via: /compartment-qr/{compartment_num}/bundle/{bundle_number}"
                                            row_warnings.extend([success_msg, navigation_msg])

                                            # Store compartment info in excel_row_data for easy access
                                            excel_data = json.loads(new_file.excel_row_data)
                                            excel_data['_compartment_assignment'] = {
                                                'compartment_number': compartment_num,
                                                'bundle_number': bundle_number,
                                                'assignment_timestamp': datetime.utcnow().isoformat(),
                                                'access_url': f"/compartment-qr/{compartment_num}/bundle/{bundle_number}"
                                            }
                                            new_file.excel_row_data = json.dumps(excel_data)

                                        elif assignment_error:
                                            row_warnings.append(f"⚠️ Bundle assignment warning: {assignment_error}")

                                    except (ValueError, TypeError):
                                        row_warnings.append(f"❌ Invalid bundle number format: {bundle_no} (must be 1-800)")
                                else:
                                    row_warnings.append("⚠️ No bundle number provided - file not assigned to compartment")

                            except Exception as bundle_error:
                                row_warnings.append(f"❌ Bundle assignment error: {str(bundle_error)}")

                            # Track compartment assignment statistics for summary
                            if compartment_assignment_success and 'compartment_info' in locals():
                                if compartment_info['compartment_number'] == 1:
                                    compartment_stats['compartment_1'] += 1
                                elif compartment_info['compartment_number'] == 2:
                                    compartment_stats['compartment_2'] += 1
                            else:
                                compartment_stats['unassigned'] += 1

                            # Generate QR code with retry mechanism
                            qr_generation_success = False
                            qr_attempts = 0
                            max_qr_attempts = 3

                            while not qr_generation_success and qr_attempts < max_qr_attempts:
                                try:
                                    # Extract key data for QR code
                                    ref_id = safe_extract_field(row, 'RefID')
                                    file_no = safe_extract_field(row, 'FILE_NO', f'FILE_{index+1}')
                                    rack_no = safe_extract_field(row, 'RackNo', '1')
                                    row_no = safe_extract_field(row, 'RowNo', '1')

                                    qr_data = {
                                        'file_id': new_file.id,
                                        'title': title[:100],  # Truncate only for QR, not database
                                        'file_no': file_no,
                                        'ref_id': ref_id,
                                        'location': f"Rack: {rack_no}, Row: {row_no}",
                                        'url': f"http://127.0.0.1:5001/files/{new_file.id}",
                                        'type': 'T-Office-File',
                                        'batch_id': batch_id
                                    }

                                    qr = qrcode.QRCode(
                                        version=1,
                                        error_correction=qrcode.constants.ERROR_CORRECT_M,
                                        box_size=10,
                                        border=4,
                                    )

                                    qr.add_data(json.dumps(qr_data))
                                    qr.make(fit=True)

                                    qr_img = qr.make_image(fill_color="black", back_color="white")
                                    qr_filename = f"qr_{new_file.id}_{batch_id}.png"
                                    qr_path = os.path.join(app.config['QR_CODE_FOLDER'], qr_filename)
                                    qr_img.save(qr_path)

                                    # Update file with QR code path
                                    new_file.qr_code = qr_filename
                                    qr_generation_success = True

                                except Exception as qr_error:
                                    qr_attempts += 1
                                    row_warnings.append(f"QR generation attempt {qr_attempts} failed: {str(qr_error)}")
                                    if qr_attempts >= max_qr_attempts:
                                        row_warnings.append("QR code generation failed after 3 attempts, continuing without QR")
                                        break

                            # Create access log for tracking
                            try:
                                access_log = AccessLog(
                                    file_id=new_file.id,
                                    user_id=current_user.id,
                                    action=f"bulk_import_batch_{batch_id}"
                                )
                                db.session.add(access_log)
                            except Exception as log_error:
                                row_warnings.append(f"Access log creation failed: {str(log_error)}")

                            # Commit individual row - CRITICAL for data preservation
                            db.session.commit()
                            created_count += 1

                            # Update warnings if any occurred
                            if row_warnings:
                                warnings_count += 1
                                all_warnings.extend([f"Row {index+1}: {w}" for w in row_warnings])

                                # Update the file record with final warnings
                                try:
                                    new_file.import_warnings = json.dumps(row_warnings)
                                    db.session.commit()
                                except:
                                    pass  # Don't fail if warning update fails

                        except Exception as row_db_error:
                            # CRITICAL: Even if database operations fail, preserve the data
                            try:
                                db.session.rollback()

                                # Create a fallback record with minimal data to ensure preservation
                                fallback_location = Location(rack_number='1', row_number='1', position='1')
                                db.session.add(fallback_location)
                                db.session.flush()

                                fallback_file = File(
                                    title=f"FALLBACK_IMPORT_Row_{index+1}",
                                    description=f"Fallback import for row {index+1}",
                                    excel_row_data=excel_row_json,  # Preserve complete data
                                    import_warnings=json.dumps([f"Database error: {str(row_db_error)}"] + row_warnings),
                                    data_quality_score=0,  # Mark as problematic but preserved
                                    import_batch_id=batch_id,
                                    original_row_number=index + 1,
                                    location_id=fallback_location.id,
                                    user_id=current_user.id
                                )
                                db.session.add(fallback_file)
                                db.session.commit()

                                created_count += 1
                                warnings_count += 1
                                all_warnings.append(f"Row {index+1}: Database error, created fallback record: {str(row_db_error)}")

                            except Exception as fallback_error:
                                # Last resort: Log the complete row data for manual recovery
                                all_warnings.append(f"Row {index+1}: CRITICAL - Complete failure, data logged: {excel_row_json}")
                                warnings_count += 1

                    except Exception as outer_error:
                        # Absolute last resort - should never happen with our error handling
                        try:
                            excel_row_json = preserve_excel_row_data(row, index + 1)
                            all_warnings.append(f"Row {index+1}: OUTER EXCEPTION - Data preserved: {excel_row_json}")
                            warnings_count += 1
                        except:
                            all_warnings.append(f"Row {index+1}: COMPLETE FAILURE - Could not preserve data")
                            warnings_count += 1



            # Calculate final success metrics
            success_rate = (created_count / total_rows * 100) if total_rows > 0 else 100

            # Prepare comprehensive summary report with compartment assignment details
            summary = {
                'total_rows': total_rows,
                'created_count': created_count,
                'warnings_count': warnings_count,
                'success_rate': round(success_rate, 2),
                'batch_id': batch_id,
                'warnings': all_warnings[:20],  # Show first 20 warnings
                'data_preservation_status': 'COMPLETE' if created_count == total_rows else 'PARTIAL',
                'import_method': '100% Data Preservation Mode',
                'compartment_assignment': {
                    'compartment_1_files': compartment_stats['compartment_1'],
                    'compartment_2_files': compartment_stats['compartment_2'],
                    'unassigned_files': compartment_stats['unassigned'],
                    'assignment_rate': round(((compartment_stats['compartment_1'] + compartment_stats['compartment_2']) / total_rows * 100), 2) if total_rows > 0 else 0,
                    'compartment_1_url': '/compartment-qr/1',
                    'compartment_2_url': '/compartment-qr/2'
                }
            }

            # Flash comprehensive success messages with compartment assignment details
            if created_count == total_rows:
                flash(f'🎉 100% SUCCESS: All {total_rows} rows imported successfully!', 'success')
            else:
                flash(f'✅ PRESERVED: {created_count} of {total_rows} rows imported ({success_rate}%)', 'success')

            # Flash compartment assignment summary
            if compartment_stats['compartment_1'] > 0 or compartment_stats['compartment_2'] > 0:
                assignment_rate = summary['compartment_assignment']['assignment_rate']
                flash(f'📦 COMPARTMENT ASSIGNMENT: {assignment_rate}% of files assigned to compartments', 'success')

                if compartment_stats['compartment_1'] > 0:
                    flash(f'📍 Compartment 1: {compartment_stats["compartment_1"]} files (Bundles 1-400)', 'info')

                if compartment_stats['compartment_2'] > 0:
                    flash(f'📍 Compartment 2: {compartment_stats["compartment_2"]} files (Bundles 401-800)', 'info')

                if compartment_stats['unassigned'] > 0:
                    flash(f'⚠️ Unassigned: {compartment_stats["unassigned"]} files (missing/invalid bundle numbers)', 'warning')

            if warnings_count > 0:
                flash(f'⚠️ {warnings_count} rows had data quality warnings but were preserved', 'warning')

            flash(f'📊 Batch ID: {batch_id} - Use this ID to track imported records', 'info')

            return render_template('admin_bulk_upload.html', summary=summary)

        except Exception as e:
            # Even if the entire process fails, try to preserve what we can
            try:
                flash(f'⚠️ Processing error occurred: {str(e)}', 'error')
                flash(f'✅ Data preservation attempted - Check logs for batch {batch_id if "batch_id" in locals() else "unknown"}', 'warning')

                # Return partial results if any were created
                if 'created_count' in locals() and created_count > 0:
                    summary = {
                        'total_rows': total_rows if 'total_rows' in locals() else 0,
                        'created_count': created_count,
                        'warnings_count': warnings_count if 'warnings_count' in locals() else 0,
                        'success_rate': (created_count / total_rows * 100) if 'total_rows' in locals() and total_rows > 0 else 0,
                        'batch_id': batch_id if 'batch_id' in locals() else 'unknown',
                        'warnings': [f"Processing error: {str(e)}"],
                        'data_preservation_status': 'PARTIAL',
                        'import_method': '100% Data Preservation Mode (Error Recovery)'
                    }
                    return render_template('admin_bulk_upload.html', summary=summary)
            except:
                pass

            return redirect(request.url)

    return render_template('admin_bulk_upload.html')

# Database migration route removed for security - use fix_database_schema.py script if needed

@app.route('/admin/security-audit')
@admin_required
def security_audit():
    """Security audit dashboard for bulk data access"""
    try:
        # Get bulk data access statistics
        total_bulk_files = File.query.filter(File.import_batch_id.isnot(None)).count()

        # Get recent bulk data access logs
        recent_access = AccessLog.query.filter_by(is_bulk_data=True)\
                                      .order_by(AccessLog.timestamp.desc())\
                                      .limit(50).all()

        # Get active sessions
        active_sessions = BulkDataAccessSession.query.filter_by(is_active=True)\
                                                    .order_by(BulkDataAccessSession.created_at.desc())\
                                                    .all()

        # Get failed access attempts
        failed_attempts = AccessLog.query.filter_by(is_bulk_data=True, access_granted=False)\
                                        .order_by(AccessLog.timestamp.desc())\
                                        .limit(20).all()

        # Calculate statistics
        stats = {
            'total_bulk_files': total_bulk_files,
            'active_sessions': len(active_sessions),
            'recent_access_count': len(recent_access),
            'failed_attempts_count': len(failed_attempts)
        }

        return render_template('admin/security_audit.html',
                             stats=stats,
                             recent_access=recent_access,
                             active_sessions=active_sessions,
                             failed_attempts=failed_attempts)

    except Exception as e:
        flash(f'Error loading security audit: {str(e)}', 'error')
        return redirect(url_for('admin_bulk_upload'))

# Database schema fix route removed for security - use fix_database_schema.py script if needed

# ==================== BUNDLE MANAGEMENT ROUTES ====================

@app.route('/bundles')
@login_required
def bundle_list():
    """Display all bundles with statistics"""
    try:
        # Initialize bundles if they don't exist
        BundleManager.initialize_bundles()

        # Get all bundles
        bundles = Bundle.query.order_by(Bundle.bundle_number).all()

        # Get bundle statistics
        bundle_stats = BundleManager.get_bundle_statistics()

        # Get compartment distribution
        compartment_distribution = BundleManager.get_bundle_distribution_by_compartment()

        return render_template('bundles/bundle_list.html',
                             bundles=bundles,
                             bundle_stats=bundle_stats,
                             compartment_distribution=compartment_distribution)

    except Exception as e:
        flash(f'Error loading bundles: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

@app.route('/bundles/<int:bundle_number>')
@login_required
def bundle_detail(bundle_number):
    """Display detailed view of a specific bundle"""
    try:
        # Validate bundle number
        if bundle_number < 1 or bundle_number > 800:
            flash('Invalid bundle number. Bundles range from 1 to 800.', 'error')
            return redirect(url_for('bundle_list'))

        # Get or create bundle
        bundle = Bundle.query.filter_by(bundle_number=bundle_number).first()
        if not bundle:
            BundleManager.initialize_bundles()
            bundle = Bundle.query.filter_by(bundle_number=bundle_number).first()

        if not bundle:
            flash(f'Bundle {bundle_number} not found.', 'error')
            return redirect(url_for('bundle_list'))

        # Update file count
        bundle.update_file_count()

        # Get files in this bundle
        files = bundle.get_files()

        # Apply security filtering
        filtered_files = []
        for file in files:
            file_dict = {'file': file}
            filtered_files.append(file_dict)

        # Apply bulk data security filtering
        secure_files = filter_bulk_data_results(filtered_files, current_user.role)

        # Get bundle statistics
        bundle_stats = BundleStatistics.calculate_for_bundle(bundle.id)

        # Get active bulk access session
        active_session = get_active_bulk_session(current_user.id)

        return render_template('bundles/bundle_detail.html',
                             bundle=bundle,
                             files=secure_files,
                             bundle_stats=bundle_stats,
                             active_session=active_session)

    except Exception as e:
        flash(f'Error loading bundle {bundle_number}: {str(e)}', 'error')
        return redirect(url_for('bundle_list'))

@app.route('/bundles/<int:bundle_number>/search')
@login_required
def bundle_search(bundle_number):
    """Search files within a specific bundle"""
    try:
        # Validate bundle number
        if bundle_number < 1 or bundle_number > 800:
            flash('Invalid bundle number. Bundles range from 1 to 800.', 'error')
            return redirect(url_for('bundle_list'))

        search_query = request.args.get('q', '').strip()

        # Get bundle
        bundle = Bundle.query.filter_by(bundle_number=bundle_number).first()
        if not bundle:
            flash(f'Bundle {bundle_number} not found.', 'error')
            return redirect(url_for('bundle_list'))

        # Search files in bundle
        if search_query:
            files = BundleManager.search_files_in_bundle(bundle_number, search_query)
        else:
            files = bundle.get_files()

        # Apply security filtering
        filtered_files = []
        for file in files:
            file_dict = {'file': file}
            filtered_files.append(file_dict)

        secure_files = filter_bulk_data_results(filtered_files, current_user.role)

        # Get active bulk access session
        active_session = get_active_bulk_session(current_user.id)

        return render_template('bundles/bundle_search.html',
                             bundle=bundle,
                             files=secure_files,
                             search_query=search_query,
                             active_session=active_session)

    except Exception as e:
        flash(f'Error searching bundle {bundle_number}: {str(e)}', 'error')
        return redirect(url_for('bundle_detail', bundle_number=bundle_number))

@app.route('/bundles/<int:bundle_number>/qr')
@login_required
def bundle_qr_code(bundle_number):
    """Generate and display QR code for a bundle"""
    try:
        bundle = Bundle.query.filter_by(bundle_number=bundle_number).first()
        if not bundle:
            flash(f'Bundle {bundle_number} not found.', 'error')
            return redirect(url_for('bundle_list'))

        # Generate QR code if it doesn't exist
        if not bundle.qr_code_path:
            BundleManager.generate_bundle_qr_code(bundle)

        return send_from_directory(
            app.config['QR_CODE_FOLDER'],
            bundle.qr_code_path,
            as_attachment=False
        )

    except Exception as e:
        flash(f'Error generating QR code for bundle {bundle_number}: {str(e)}', 'error')
        return redirect(url_for('bundle_detail', bundle_number=bundle_number))

@app.route('/admin/bundles/reassign', methods=['POST'])
@admin_required
def reassign_file_bundle():
    """Reassign a file to a different bundle"""
    try:
        file_id = request.form.get('file_id', type=int)
        new_bundle_number = request.form.get('new_bundle_number', type=int)
        reason = request.form.get('reason', 'Administrative reassignment')

        if not file_id or not new_bundle_number:
            flash('File ID and new bundle number are required.', 'error')
            return redirect(request.referrer or url_for('bundle_list'))

        if new_bundle_number < 1 or new_bundle_number > 800:
            flash('Invalid bundle number. Must be between 1 and 800.', 'error')
            return redirect(request.referrer or url_for('bundle_list'))

        success, message = BundleManager.reassign_file_to_bundle(
            file_id, new_bundle_number, current_user.id, reason
        )

        if success:
            flash(f'✅ {message}', 'success')
        else:
            flash(f'❌ {message}', 'error')

        return redirect(request.referrer or url_for('bundle_list'))

    except Exception as e:
        flash(f'Error reassigning file: {str(e)}', 'error')
        return redirect(request.referrer or url_for('bundle_list'))

@app.route('/admin/bundles/statistics')
@admin_required
def bundle_statistics():
    """Comprehensive bundle statistics and analytics"""
    try:
        # Get overall statistics
        bundle_stats = BundleManager.get_bundle_statistics()

        # Get compartment distribution
        compartment_distribution = BundleManager.get_bundle_distribution_by_compartment()

        # Get data integrity report
        integrity_report = BundleManager.validate_bundle_data_integrity()

        # Get recent bundle assignments
        recent_assignments = BundleAssignment.query.order_by(
            BundleAssignment.assigned_at.desc()
        ).limit(20).all()

        # Calculate additional metrics
        bundles = Bundle.query.all()
        capacity_utilization = []

        for bundle in bundles:
            bundle.update_file_count()
            capacity_utilization.append({
                'bundle_number': bundle.bundle_number,
                'utilization': bundle.get_capacity_percentage(),
                'status': bundle.get_status(),
                'file_count': bundle.current_count,
                'max_capacity': bundle.max_capacity
            })

        return render_template('admin/bundle_statistics.html',
                             bundle_stats=bundle_stats,
                             compartment_distribution=compartment_distribution,
                             integrity_report=integrity_report,
                             recent_assignments=recent_assignments,
                             capacity_utilization=capacity_utilization)

    except Exception as e:
        flash(f'Error loading bundle statistics: {str(e)}', 'error')
        return redirect(url_for('admin_bulk_upload'))

@app.route('/admin/bundles/initialize')
@admin_required
def initialize_bundles():
    """Initialize all bundle records"""
    try:
        created_bundles = BundleManager.initialize_bundles()

        if created_bundles:
            flash(f'✅ Initialized {len(created_bundles)} bundles: {", ".join(map(str, created_bundles))}', 'success')
        else:
            flash('✅ All bundles already initialized.', 'info')

        return redirect(url_for('bundle_list'))

    except Exception as e:
        flash(f'Error initializing bundles: {str(e)}', 'error')
        return redirect(url_for('bundle_list'))

@app.route('/admin/bundles/update-counts')
@admin_required
def update_bundle_counts():
    """Update file counts for all bundles"""
    try:
        updated_count = BundleManager.update_all_bundle_counts()
        flash(f'✅ Updated file counts for {updated_count} bundles.', 'success')
        return redirect(url_for('bundle_list'))

    except Exception as e:
        flash(f'Error updating bundle counts: {str(e)}', 'error')
        return redirect(url_for('bundle_list'))

@app.route('/admin/bundles/generate-qr-codes')
@admin_required
def generate_all_bundle_qr_codes():
    """Generate QR codes for all bundles"""
    try:
        bundles = Bundle.query.all()
        generated_count = 0

        for bundle in bundles:
            if BundleManager.generate_bundle_qr_code(bundle):
                generated_count += 1

        flash(f'✅ Generated QR codes for {generated_count} bundles.', 'success')
        return redirect(url_for('bundle_list'))

    except Exception as e:
        flash(f'Error generating QR codes: {str(e)}', 'error')
        return redirect(url_for('bundle_list'))

@app.route('/admin/bundles/fix-compartment-assignments')
@admin_required
def fix_compartment_assignments():
    """Fix incorrect compartment assignments for bundles"""
    try:
        fixes_applied = BundleManager.fix_compartment_assignments()

        if any("Error:" in fix for fix in fixes_applied):
            for fix in fixes_applied:
                if "Error:" in fix:
                    flash(f'❌ {fix}', 'error')
                else:
                    flash(f'ℹ️ {fix}', 'info')
        else:
            flash(f'✅ Compartment assignment fixes completed!', 'success')
            for fix in fixes_applied:
                flash(f'• {fix}', 'info')

        return redirect(url_for('bundle_list'))

    except Exception as e:
        flash(f'Error fixing compartment assignments: {str(e)}', 'error')
        return redirect(url_for('bundle_list'))

@app.route('/admin/bulk-upload/template')
@admin_required
def download_bulk_upload_template():
    """Download Excel template for bulk upload"""
    import pandas as pd
    from io import BytesIO
    from flask import Response

    # Create sample data with required columns
    sample_data = {
        'SL No': [1, 2, 3],
        'INDEX ID': ['IDX001', 'IDX002', 'IDX003'],
        'RefID': ['REF001', 'REF002', 'REF003'],
        'FILE_NO': ['FILE001', 'FILE002', 'FILE003'],
        'Category': ['Revenue', 'Land Records', 'Certificates'],
        'DisposalCat': ['Permanent', 'Temporary', 'Permanent'],
        'Createddate': ['2024-01-01', '2024-01-02', '2024-01-03'],
        'RowNo': [1, 2, 3],
        'RackNo': [1, 1, 2],
        'RecordRoomSlNo': [1, 2, 1],
        'BundleNo': [1, 1, 2],
        'Subject': ['Land Registration Document', 'Property Tax Certificate', 'Income Certificate'],
        'hobli_name': ['Chikkamagaluru', 'Kadur', 'Koppa'],
        'village_name': ['Aldur', 'Amruthapura', 'Anandapura'],
        'survey_no': ['123/1', '456/2', '789/3']
    }

    df = pd.DataFrame(sample_data)

    # Create Excel file in memory
    output = BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='Bulk Upload Template', index=False)

        # Add instructions sheet
        instructions = pd.DataFrame({
            'Instructions': [
                '🎯 100% DATA PRESERVATION MODE - ALL DATA WILL BE IMPORTED',
                '1. Fill any or all of the 15 columns with your data',
                '2. RefID duplicates are ALLOWED and will be preserved',
                '3. RackNo can be any value - invalid values get fallback defaults',
                '4. hobli_name, village_name, survey_no enable location-based searches',
                '5. ALL columns are optional - empty values are preserved',
                '6. Save as .xlsx or .xls format',
                '7. NO row limits - import files of any size',
                '8. Data quality issues are flagged but do NOT prevent import',
                '9. Every row becomes a searchable database record',
                '10. Original Excel data is preserved in JSON format for recovery',
                '11. Batch tracking enables audit trails for all imports',
                '12. Search integration works with ALL imported data including duplicates'
            ]
        })
        instructions.to_excel(writer, sheet_name='Instructions', index=False)

    output.seek(0)

    return Response(
        output.getvalue(),
        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        headers={
            'Content-Disposition': 'attachment; filename=bulk_upload_template.xlsx'
        }
    )

@app.route('/analytics')
@login_required
def analytics():
    # Get file access statistics
    file_stats = db.session.query(
        File.title,
        db.func.count(AccessLog.id).label('access_count')
    ).join(AccessLog).group_by(File.id).all()

    # Get user access statistics
    user_stats = db.session.query(
        User.username,
        db.func.count(AccessLog.id).label('access_count')
    ).join(AccessLog).group_by(User.id).all()

    return render_template('analytics.html', file_stats=file_stats, user_stats=user_stats)

# Error handlers
@app.errorhandler(404)
def not_found_error(error):
    return render_template('404.html'), 404

@app.errorhandler(500)
def internal_error(error):
    db.session.rollback()
    return render_template('500.html'), 500

# SocketIO already initialized above

# Add this route
@app.route('/collaboration')
@login_required
def collaboration():
    # Get active users and their recent activities
    active_logs = AccessLog.query.filter(
        AccessLog.timestamp >= datetime.utcnow() - timedelta(hours=1)
    ).order_by(AccessLog.timestamp.desc()).limit(20).all()

    return render_template('collaboration.html', active_logs=active_logs)

@app.route('/compartment-qr')
@login_required
def compartment_qr_management():
    """Display compartment QR code management page."""
    compartment_qrs = CompartmentQR.query.order_by(CompartmentQR.compartment_number).all()
    return render_template('compartment_qr.html', compartment_qrs=compartment_qrs)

@app.route('/compartment-qr/<int:compartment_number>')
@login_required
def view_compartment_qr(compartment_number):
    """View a specific compartment QR code."""
    compartment_qr = CompartmentQR.query.filter_by(compartment_number=compartment_number).first_or_404()
    return render_template('view_compartment_qr.html', compartment_qr=compartment_qr)

@app.route('/compartment-qr/<int:compartment_number>/image')
@login_required
def get_compartment_qr_image(compartment_number):
    """Serve the compartment QR code image."""
    compartment_qr = CompartmentQR.query.filter_by(compartment_number=compartment_number).first_or_404()

    return send_from_directory(
        app.config['QR_CODE_FOLDER'],
        compartment_qr.qr_image_path,
        as_attachment=False
    )

@app.route('/compartment-qr/<int:compartment_number>/download')
@login_required
def download_compartment_qr(compartment_number):
    """Download the compartment QR code image."""
    compartment_qr = CompartmentQR.query.filter_by(compartment_number=compartment_number).first_or_404()

    return send_from_directory(
        app.config['QR_CODE_FOLDER'],
        compartment_qr.qr_image_path,
        as_attachment=True,
        download_name=f'compartment_{compartment_number}_qr.png'
    )

@app.route('/compartment-qr/<int:compartment_number>/bundle/<int:bundle_number>')
@login_required
def view_compartment_bundle_files(compartment_number, bundle_number):
    """View files in a specific bundle within a compartment."""
    # Validate compartment number
    if compartment_number not in [1, 2]:
        flash('Invalid compartment number')
        return redirect(url_for('compartment_qr_management'))

    # Get compartment info
    compartment_qr = CompartmentQR.query.filter_by(compartment_number=compartment_number).first_or_404()

    # Validate bundle number is within compartment range
    if not (compartment_qr.bundle_range_start <= bundle_number <= compartment_qr.bundle_range_end):
        flash(f'Bundle {bundle_number} is not in Compartment {compartment_number} range ({compartment_qr.bundle_range_start}-{compartment_qr.bundle_range_end})')
        return redirect(url_for('view_compartment_qr', compartment_number=compartment_number))

    # Get all files with excel_row_data (bulk uploaded files)
    all_files = File.query.filter(File.excel_row_data.isnot(None)).all()

    # Filter files by the specific bundle number
    bundle_files = []

    for file in all_files:
        try:
            excel_data = json.loads(file.excel_row_data)
            bundle_no = excel_data.get('BundleNo', '')

            if bundle_no:
                try:
                    file_bundle_number = int(float(str(bundle_no)))
                    if file_bundle_number == bundle_number:
                        bundle_files.append({
                            'file': file,
                            'bundle_number': file_bundle_number,
                            'excel_data': excel_data,
                            'location': file.location,
                            'parsed_location': parse_location_from_description(file.description)
                        })
                except (ValueError, TypeError):
                    continue
        except (json.JSONDecodeError, AttributeError):
            continue

    # Sort files by file ID for consistent ordering
    bundle_files.sort(key=lambda x: x['file'].id)

    # Get bundle record if it exists
    bundle_record = Bundle.query.filter_by(bundle_number=bundle_number).first()

    # Log access for security (bulk data access)
    try:
        for file_data in bundle_files:
            log_access(
                action='viewed',
                file_id=file_data['file'].id,
                search_criteria=f'Compartment {compartment_number}, Bundle {bundle_number}',
                is_bulk_data=True,
                access_granted=True
            )
    except Exception as e:
        app.logger.warning(f"Failed to log bundle access: {str(e)}")

    return render_template('compartment_bundle_files.html',
                         compartment_qr=compartment_qr,
                         bundle_number=bundle_number,
                         bundle_files=bundle_files,
                         bundle_record=bundle_record,
                         total_files=len(bundle_files))

@app.route('/compartment-bundles/<int:compartment_number>')
@login_required
def compartment_bundles(compartment_number):
    """Display all files within a compartment's bundle range organized by bundles."""
    # Validate compartment number
    if compartment_number not in [1, 2]:
        flash('Invalid compartment number')
        return redirect(url_for('dashboard'))

    # Define compartment ranges
    if compartment_number == 1:
        range_start, range_end = 1, 400
    else:
        range_start, range_end = 401, 800

    # Get search parameters
    search_bundle = request.args.get('bundle', type=int)
    search_ref_id = request.args.get('ref_id', '')
    page = request.args.get('page', 1, type=int)
    per_page = 50  # Files per page

    # Get all files with excel_row_data (bulk uploaded files)
    all_files = File.query.filter(File.excel_row_data.isnot(None)).all()

    # Filter files by compartment range based on BundleNo in excel_row_data
    compartment_files = []
    unique_bundles = set()

    for file in all_files:
        try:
            excel_data = json.loads(file.excel_row_data)
            bundle_no = excel_data.get('BundleNo', '')

            if bundle_no:
                try:
                    bundle_number = int(float(str(bundle_no)))
                    # Check if bundle belongs to this compartment
                    if range_start <= bundle_number <= range_end:
                        # Apply search filters
                        include_file = True

                        # Filter by specific bundle if requested
                        if search_bundle and bundle_number != search_bundle:
                            include_file = False

                        # Filter by RefID if provided
                        if search_ref_id and include_file:
                            ref_id = excel_data.get('RefID', '')
                            if search_ref_id.lower() not in str(ref_id).lower():
                                include_file = False

                        if include_file:
                            compartment_files.append({
                                'file': file,
                                'bundle_number': bundle_number,
                                'excel_data': excel_data,
                                'location': file.location,
                                'parsed_location': parse_location_from_description(file.description)
                            })
                            unique_bundles.add(bundle_number)

                except (ValueError, TypeError):
                    continue
        except (json.JSONDecodeError, AttributeError):
            continue

    # Sort files by bundle number, then by file ID
    compartment_files.sort(key=lambda x: (x['bundle_number'], x['file'].id))

    # Paginate results
    total_files = len(compartment_files)
    start_idx = (page - 1) * per_page
    end_idx = start_idx + per_page
    paginated_files = compartment_files[start_idx:end_idx]

    # Create pagination object manually
    class SimplePagination:
        def __init__(self, page, per_page, total, items):
            self.page = page
            self.per_page = per_page
            self.total = total
            self.items = items
            self.pages = (total + per_page - 1) // per_page
            self.has_prev = page > 1
            self.has_next = page < self.pages
            self.prev_num = page - 1 if self.has_prev else None
            self.next_num = page + 1 if self.has_next else None

    files_pagination = SimplePagination(page, per_page, total_files, paginated_files)

    # Group files by bundle for better organization
    files_by_bundle = {}
    for file_data in paginated_files:
        bundle_num = file_data['bundle_number']
        if bundle_num not in files_by_bundle:
            files_by_bundle[bundle_num] = []
        files_by_bundle[bundle_num].append(file_data)

    return render_template('compartment_bundles.html',
                         compartment_number=compartment_number,
                         range_start=range_start,
                         range_end=range_end,
                         files=paginated_files,
                         files_by_bundle=files_by_bundle,
                         files_pagination=files_pagination,
                         total_files=total_files,
                         unique_bundles=sorted(list(unique_bundles)),
                         search_bundle=search_bundle,
                         search_ref_id=search_ref_id,
                         total_bundles=len(unique_bundles))

@app.route('/bulk-data-unlock', methods=['GET', 'POST'])
@login_required
def bulk_data_unlock():
    """Secure interface for unlocking bulk uploaded data access"""
    if request.method == 'POST':
        # Extract search criteria
        search_criteria = {
            'ref_id': request.form.get('ref_id', '').strip(),
            'hobli_name': request.form.get('hobli_name', '').strip(),
            'village_name': request.form.get('village_name', '').strip(),
            'survey_no': request.form.get('survey_no', '').strip(),
            'file_no': request.form.get('file_no', '').strip(),
            'date_range': request.form.get('date_range', '').strip()
        }

        # Validate criteria
        is_valid, message = validate_search_criteria(search_criteria)
        if not is_valid:
            flash(f'Access denied: {message}', 'error')
            return render_template('bulk_data_unlock.html')

        # Create access session
        session_obj, session_message = create_bulk_access_session(current_user.id, search_criteria)
        if session_obj:
            flash(f'✅ {session_message} Session expires in 30 minutes.', 'success')
            # Redirect to compartment search with criteria
            return redirect(url_for('compartment_search', **{k: v for k, v in search_criteria.items() if v}))
        else:
            flash(f'❌ {session_message}', 'error')

    # Check for existing active session
    active_session = get_active_bulk_session(current_user.id)

    return render_template('bulk_data_unlock.html', active_session=active_session)

@app.route('/bulk-data-session/status')
@login_required
def bulk_data_session_status():
    """Get current bulk data access session status"""
    active_session = get_active_bulk_session(current_user.id)

    if active_session:
        return jsonify({
            'has_session': True,
            'time_remaining': active_session.time_remaining(),
            'access_count': active_session.access_count,
            'criteria_count': active_session.criteria_count,
            'created_at': active_session.created_at.isoformat()
        })
    else:
        return jsonify({'has_session': False})

@app.route('/bulk-data-session/extend', methods=['POST'])
@login_required
def extend_bulk_data_session():
    """Extend current bulk data access session"""
    active_session = get_active_bulk_session(current_user.id)

    if active_session and active_session.extend_session():
        db.session.commit()
        return jsonify({
            'success': True,
            'time_remaining': active_session.time_remaining(),
            'message': 'Session extended successfully'
        })
    else:
        return jsonify({
            'success': False,
            'message': 'No active session to extend'
        })

@app.route('/bulk-data-session/revoke', methods=['POST'])
@login_required
def revoke_bulk_data_session():
    """Revoke current bulk data access session"""
    active_session = get_active_bulk_session(current_user.id)

    if active_session:
        active_session.revoke()
        db.session.commit()
        return jsonify({
            'success': True,
            'message': 'Session revoked successfully'
        })
    else:
        return jsonify({
            'success': False,
            'message': 'No active session to revoke'
        })

@app.route('/compartment-search')
@login_required
def compartment_search():
    """Compartment search interface for QR code scanning with bulk data security."""
    # Get compartment info from URL parameters (from QR scan)
    compartment = request.args.get('compartment', type=int)
    range_start = request.args.get('range_start', type=int)
    range_end = request.args.get('range_end', type=int)

    # Get search parameters
    ref_id = request.args.get('ref_id', '')
    taluk_name = request.args.get('taluk_name', '')
    hobli_name = request.args.get('hobli_name', '')
    village_name = request.args.get('village_name', '')
    survey_no = request.args.get('survey_no', '')

    # Get unique location values for dropdowns
    location_data = get_location_dropdown_data()

    # Initialize search results
    search_results = []
    search_performed = False
    bulk_data_warning = False

    # Perform search if any search criteria provided
    if ref_id or taluk_name or hobli_name or village_name or survey_no:
        raw_results = search_files_by_criteria(
            ref_id=ref_id,
            taluk_name=taluk_name,
            hobli_name=hobli_name,
            village_name=village_name,
            survey_no=survey_no,
            compartment=compartment
        )

        # Apply security filtering for bulk data
        search_results = filter_bulk_data_results(raw_results, current_user.role)
        search_performed = True

        # Check if results contain bulk data that requires additional verification
        bulk_data_count = sum(1 for result in search_results
                             if isinstance(result, dict) and result.get('is_bulk_data'))
        if bulk_data_count > 0 and current_user.role != 'Administrator':
            active_session = get_active_bulk_session(current_user.id)
            if not active_session:
                bulk_data_warning = True

    # Get current bulk access session info
    active_session = get_active_bulk_session(current_user.id)

    return render_template('compartment_search.html',
                         compartment=compartment,
                         range_start=range_start,
                         range_end=range_end,
                         ref_id=ref_id,
                         taluk_name=taluk_name,
                         hobli_name=hobli_name,
                         village_name=village_name,
                         survey_no=survey_no,
                         location_data=location_data,
                         search_results=search_results,
                         search_performed=search_performed,
                         bulk_data_warning=bulk_data_warning,
                         active_session=active_session)

def get_location_dropdown_data():
    """Get unique location values for dropdown filters."""
    try:
        # Query unique values from file descriptions (which contain location data from Excel imports)
        files_with_descriptions = File.query.filter(File.description.isnot(None)).all()

        taluk_names = set()
        hobli_names = set()
        village_names = set()

        for file in files_with_descriptions:
            if file.description:
                # Parse description for location data (format: "Taluk: X | Hobli: Y | Village: Z | ...")
                desc_parts = file.description.split(' | ')
                for part in desc_parts:
                    if part.startswith('Taluk: '):
                        taluk_names.add(part.replace('Taluk: ', '').strip())
                    elif part.startswith('Hobli: '):
                        hobli_names.add(part.replace('Hobli: ', '').strip())
                    elif part.startswith('Village: '):
                        village_names.add(part.replace('Village: ', '').strip())

        # Always add taluk names (merge with existing if any)
        taluk_names.update({
            'Chikkamagaluru', 'Kadur', 'Koppa', 'Mudigere', 'Narasimharajapura',
            'Sringeri', 'Tarikere', 'Ajjampura'
        })

        # Always add hobli names (merge with existing if any)
        hobli_names.update({
            'Chikkamagaluru', 'Kadur', 'Koppa', 'Mudigere', 'Narasimharajapura',
            'Sringeri', 'Tarikere', 'Ajjampura', 'Balehonnur', 'Kalasa',
            'Kottigehara', 'Lakya', 'Mallandur', 'N.R.Pura', 'Rippanpet'
        })

        # Always add village names (merge with existing if any)
        village_names.update({
            'Aldur', 'Amruthapura', 'Anandapura', 'Aralaguppe', 'Arehalli',
            'Balehonnur', 'Bannikodu', 'Basavapatna', 'Belagodu', 'Belur',
            'Bhadravathi', 'Birur', 'Channagiri', 'Chikkamagaluru', 'Davangere',
            'Gonibeedu', 'Hariharapura', 'Horanadu', 'Kadur', 'Kalasa',
            'Kemmanagundi', 'Koppa', 'Kottigehara', 'Kudremukh', 'Lakya',
            'Mallandur', 'Mudigere', 'Narasimharajapura', 'Rippanpet', 'Sringeri',
            'Tarikere', 'Thirthahalli', 'Udupi', 'Varaha'
        })

        return {
            'taluk_names': sorted(list(taluk_names)),
            'hobli_names': sorted(list(hobli_names)),
            'village_names': sorted(list(village_names))
        }
    except Exception as e:
        print(f"Error getting location data: {e}")
        # Return dummy data as fallback
        return {
            'taluk_names': sorted(['Chikkamagaluru', 'Kadur', 'Koppa', 'Mudigere', 'Narasimharajapura', 'Sringeri', 'Tarikere', 'Ajjampura']),
            'hobli_names': sorted(['Chikkamagaluru', 'Kadur', 'Koppa', 'Mudigere', 'Narasimharajapura', 'Sringeri', 'Tarikere', 'Ajjampura', 'Balehonnur', 'Kalasa', 'Kottigehara', 'Lakya', 'Mallandur', 'N.R.Pura', 'Rippanpet']),
            'village_names': sorted(['Aldur', 'Amruthapura', 'Anandapura', 'Aralaguppe', 'Arehalli', 'Balehonnur', 'Bannikodu', 'Basavapatna', 'Belagodu', 'Belur', 'Bhadravathi', 'Birur', 'Channagiri', 'Chikkamagaluru', 'Davangere', 'Gonibeedu', 'Hariharapura', 'Horanadu', 'Kadur', 'Kalasa', 'Kemmanagundi', 'Koppa', 'Kottigehara', 'Kudremukh', 'Lakya', 'Mallandur', 'Mudigere', 'Narasimharajapura', 'Rippanpet', 'Sringeri', 'Tarikere', 'Thirthahalli', 'Udupi', 'Varaha'])
        }

def search_files_by_criteria(ref_id='', taluk_name='', hobli_name='', village_name='', survey_no='', compartment=None):
    """Search files based on various criteria."""
    try:
        # Start with base query
        query = File.query.join(Location)

        # Filter by compartment range if specified
        if compartment:
            if compartment == 1:
                # Compartment 1: bundles 1-400
                query = query.filter(Location.rack_number.between('1', '400'))
            elif compartment == 2:
                # Compartment 2: bundles 401-800
                query = query.filter(Location.rack_number.between('401', '800'))

        # Filter by RefID in description
        if ref_id:
            query = query.filter(File.description.contains(f'RefID: {ref_id}'))

        # Filter by location data in description (handle both old and new formats)
        if taluk_name:
            query = query.filter(db.or_(
                File.description.contains(f'Taluk: {taluk_name}'),
                File.description.contains(f'taluk_name: {taluk_name}')
            ))

        if hobli_name:
            query = query.filter(db.or_(
                File.description.contains(f'Hobli: {hobli_name}'),
                File.description.contains(f'hobli_name: {hobli_name}')
            ))

        if village_name:
            query = query.filter(db.or_(
                File.description.contains(f'Village: {village_name}'),
                File.description.contains(f'village_name: {village_name}')
            ))

        if survey_no:
            query = query.filter(db.or_(
                File.description.contains(f'Survey No: {survey_no}'),
                File.description.contains(f'survey_no: {survey_no}')
            ))

        # Execute query and get results
        results = query.order_by(File.created_at.desc()).limit(50).all()

        # Format results for display
        formatted_results = []
        for file in results:
            result_data = {
                'file': file,
                'location': file.location,
                'bundle_number': file.location.rack_number if file.location else 'N/A',
                'row_number': file.location.row_number if file.location else 'N/A',
                'position': file.location.position if file.location else 'N/A',
                'parsed_location': parse_location_from_description(file.description)
            }
            formatted_results.append(result_data)

        return formatted_results

    except Exception as e:
        print(f"Error searching files: {e}")
        return []

def parse_location_from_description(description):
    """Parse location information from file description (handles both old and new formats)."""
    if not description:
        return {}

    location_info = {}
    desc_parts = description.split(' | ')

    for part in desc_parts:
        part = part.strip()
        if part.startswith('RefID: '):
            location_info['ref_id'] = part.replace('RefID: ', '').strip()
        elif part.startswith('Taluk: ') or part.startswith('taluk_name: '):
            location_info['taluk'] = part.replace('Taluk: ', '').replace('taluk_name: ', '').strip()
        elif part.startswith('Hobli: ') or part.startswith('hobli_name: '):
            location_info['hobli'] = part.replace('Hobli: ', '').replace('hobli_name: ', '').strip()
        elif part.startswith('Village: ') or part.startswith('village_name: '):
            location_info['village'] = part.replace('Village: ', '').replace('village_name: ', '').strip()
        elif part.startswith('Survey No: ') or part.startswith('survey_no: '):
            location_info['survey_no'] = part.replace('Survey No: ', '').replace('survey_no: ', '').strip()
        elif part.startswith('District: ') or part.startswith('district_name: '):
            location_info['district'] = part.replace('District: ', '').replace('district_name: ', '').strip()

    return location_info

# Add this socket event
@socketio.on('file_access')
def handle_file_access(data):
    # Broadcast to all connected clients
    emit('file_activity', {
        'user': current_user.username,
        'file_id': data['file_id'],
        'file_title': data['file_title'],
        'action': data['action'],
        'timestamp': datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')
    }, broadcast=True)

# Duplicate error handler removed

if __name__ == '__main__':
    # Create all database tables and default users
    with app.app_context():
        db.create_all()

        # Create default users if they don't exist
        if not User.query.filter_by(username='admin').first():
            admin_user = User(username='admin', email='<EMAIL>', role='Administrator')
            admin_user.set_password('admin123')
            db.session.add(admin_user)

        if not User.query.filter_by(username='officer').first():
            officer_user = User(username='officer', email='<EMAIL>', role='Officer')
            officer_user.set_password('officer123')
            db.session.add(officer_user)

        if not User.query.filter_by(username='clerk').first():
            clerk_user = User(username='clerk', email='<EMAIL>', role='Clerk')
            clerk_user.set_password('clerk123')
            db.session.add(clerk_user)

        db.session.commit()
        print("Database initialized with default users:")
        print("Administrator: admin/admin123")
        print("Officer: officer/officer123")
        print("Clerk: clerk/clerk123")

    # Production-ready settings
    debug_mode = os.environ.get('FLASK_ENV') == 'development'
    app.run(debug=debug_mode, host='127.0.0.1', port=5001, threaded=True)