#!/usr/bin/env python3
"""
Database Connection Diagnostic Tool for T-Office Flask Project
This script will help identify and resolve database protection/security issues
"""

import os
import sys
import sqlite3
import traceback
from pathlib import Path

def test_sqlite_permissions():
    """Test SQLite database file permissions and access"""
    print("🔍 Testing SQLite Database Permissions...")
    print("=" * 50)
    
    # Check main database file
    db_files = [
        'toffice.db',
        'instance/taluk_office.db'
    ]
    
    for db_file in db_files:
        print(f"\n📁 Checking: {db_file}")
        
        if os.path.exists(db_file):
            # Check file permissions
            file_stat = os.stat(db_file)
            print(f"   ✅ File exists")
            print(f"   📊 Size: {file_stat.st_size} bytes")
            print(f"   🔐 Permissions: {oct(file_stat.st_mode)[-3:]}")
            
            # Test read access
            try:
                with open(db_file, 'rb') as f:
                    f.read(1)
                print(f"   ✅ Read access: OK")
            except Exception as e:
                print(f"   ❌ Read access: FAILED - {e}")
                return False
            
            # Test write access
            try:
                with open(db_file, 'ab') as f:
                    pass
                print(f"   ✅ Write access: OK")
            except Exception as e:
                print(f"   ❌ Write access: FAILED - {e}")
                return False
            
            # Test SQLite connection
            try:
                conn = sqlite3.connect(db_file)
                cursor = conn.cursor()
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                tables = cursor.fetchall()
                print(f"   ✅ SQLite connection: OK")
                print(f"   📋 Tables found: {len(tables)}")
                for table in tables:
                    print(f"      - {table[0]}")
                conn.close()
            except Exception as e:
                print(f"   ❌ SQLite connection: FAILED - {e}")
                return False
        else:
            print(f"   ❌ File does not exist")
    
    return True

def test_flask_app_database():
    """Test Flask app database configuration"""
    print("\n🔍 Testing Flask App Database Configuration...")
    print("=" * 50)
    
    try:
        # Import Flask app components
        sys.path.insert(0, os.getcwd())
        from config import Config
        
        print(f"✅ Config imported successfully")
        
        # Check database URI
        config = Config()
        db_uri = config.SQLALCHEMY_DATABASE_URI
        print(f"📊 Database URI: {db_uri}")
        
        if db_uri.startswith('sqlite:///'):
            db_path = db_uri[10:]  # Remove 'sqlite:///'
            print(f"📁 Database path: {db_path}")
            
            if os.path.exists(db_path):
                print(f"✅ Database file exists")
            else:
                print(f"❌ Database file missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Flask config test failed: {e}")
        traceback.print_exc()
        return False

def test_flask_app_startup():
    """Test Flask app startup with database"""
    print("\n🔍 Testing Flask App Startup...")
    print("=" * 50)
    
    try:
        # Import and test Flask app
        from flask import Flask
        from config import Config
        
        app = Flask(__name__)
        app.config.from_object(Config)
        
        print(f"✅ Flask app created")
        
        # Test database initialization
        with app.app_context():
            from extensions import db
            db.init_app(app)
            
            print(f"✅ Database extension initialized")
            
            # Test database connection (SQLAlchemy 2.x compatible)
            try:
                from sqlalchemy import text
                with db.engine.connect() as connection:
                    result = connection.execute(text("SELECT 1"))
                    result.fetchone()
                print(f"✅ Database connection successful")
            except Exception as e:
                print(f"❌ Database connection failed: {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Flask app startup failed: {e}")
        traceback.print_exc()
        return False

def fix_database_permissions():
    """Fix common database permission issues"""
    print("\n🔧 Fixing Database Permissions...")
    print("=" * 50)
    
    db_files = ['toffice.db', 'instance/taluk_office.db']
    
    for db_file in db_files:
        if os.path.exists(db_file):
            try:
                # Ensure directory exists and is writable
                db_dir = os.path.dirname(db_file) or '.'
                os.makedirs(db_dir, exist_ok=True)
                
                # Set file permissions (readable and writable)
                os.chmod(db_file, 0o666)
                print(f"✅ Fixed permissions for {db_file}")
                
            except Exception as e:
                print(f"❌ Failed to fix permissions for {db_file}: {e}")

def create_clean_database():
    """Create a clean database with proper permissions"""
    print("\n🔧 Creating Clean Database...")
    print("=" * 50)
    
    try:
        # Remove existing problematic databases
        db_files = ['toffice.db', 'instance/taluk_office.db']
        for db_file in db_files:
            if os.path.exists(db_file):
                try:
                    os.remove(db_file)
                    print(f"✅ Removed old database: {db_file}")
                except Exception as e:
                    print(f"❌ Could not remove {db_file}: {e}")
        
        # Create new database with proper permissions
        new_db = 'toffice.db'
        conn = sqlite3.connect(new_db)
        conn.execute("CREATE TABLE IF NOT EXISTS test_table (id INTEGER PRIMARY KEY)")
        conn.commit()
        conn.close()
        
        # Set proper permissions
        os.chmod(new_db, 0o666)
        print(f"✅ Created clean database: {new_db}")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to create clean database: {e}")
        return False

def main():
    """Main diagnostic function"""
    print("🏛️  T-Office Database Security Diagnostic Tool")
    print("=" * 60)
    print("This tool will help identify and resolve database protection issues")
    print()
    
    # Step 1: Test SQLite permissions
    sqlite_ok = test_sqlite_permissions()
    
    # Step 2: Test Flask configuration
    config_ok = test_flask_app_database()
    
    # Step 3: Test Flask app startup
    startup_ok = test_flask_app_startup()
    
    print("\n" + "=" * 60)
    print("📊 DIAGNOSTIC SUMMARY")
    print("=" * 60)
    print(f"SQLite Permissions: {'✅ PASS' if sqlite_ok else '❌ FAIL'}")
    print(f"Flask Configuration: {'✅ PASS' if config_ok else '❌ FAIL'}")
    print(f"Flask App Startup: {'✅ PASS' if startup_ok else '❌ FAIL'}")
    
    if not all([sqlite_ok, config_ok, startup_ok]):
        print("\n🔧 ATTEMPTING FIXES...")
        print("=" * 60)
        
        # Try to fix permissions
        fix_database_permissions()
        
        # If still failing, create clean database
        if not sqlite_ok:
            create_clean_database()
        
        print("\n✅ Fixes applied. Please run the diagnostic again.")
    else:
        print("\n🎉 All tests passed! Database should work correctly.")

if __name__ == "__main__":
    main()
