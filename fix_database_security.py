#!/usr/bin/env python3
"""
Database Security Configuration Fix for T-Office Flask Project
This script resolves all database protection and permission issues
"""

import os
import sqlite3
import shutil
from pathlib import Path

def ensure_database_permissions():
    """Ensure proper database file permissions"""
    print("🔧 Setting Database Permissions...")
    
    # Database files to check
    db_files = [
        'toffice.db',
        'instance/taluk_office.db'
    ]
    
    for db_file in db_files:
        if os.path.exists(db_file):
            try:
                # Set read/write permissions for owner and group
                os.chmod(db_file, 0o666)
                print(f"✅ Set permissions for {db_file}")
            except Exception as e:
                print(f"❌ Failed to set permissions for {db_file}: {e}")
        
        # Ensure directory permissions
        db_dir = os.path.dirname(db_file)
        if db_dir and os.path.exists(db_dir):
            try:
                os.chmod(db_dir, 0o755)
                print(f"✅ Set directory permissions for {db_dir}")
            except Exception as e:
                print(f"❌ Failed to set directory permissions for {db_dir}: {e}")

def remove_database_locks():
    """Remove any SQLite database locks"""
    print("🔧 Removing Database Locks...")
    
    lock_files = [
        'toffice.db-wal',
        'toffice.db-shm',
        'instance/taluk_office.db-wal',
        'instance/taluk_office.db-shm'
    ]
    
    for lock_file in lock_files:
        if os.path.exists(lock_file):
            try:
                os.remove(lock_file)
                print(f"✅ Removed lock file: {lock_file}")
            except Exception as e:
                print(f"❌ Failed to remove lock file {lock_file}: {e}")

def optimize_database():
    """Optimize database for better performance and security"""
    print("🔧 Optimizing Database...")
    
    db_files = ['toffice.db', 'instance/taluk_office.db']
    
    for db_file in db_files:
        if os.path.exists(db_file):
            try:
                conn = sqlite3.connect(db_file)
                
                # Enable WAL mode for better concurrency
                conn.execute("PRAGMA journal_mode=WAL")
                
                # Set secure delete
                conn.execute("PRAGMA secure_delete=ON")
                
                # Optimize database
                conn.execute("VACUUM")
                conn.execute("ANALYZE")
                
                conn.close()
                print(f"✅ Optimized database: {db_file}")
                
            except Exception as e:
                print(f"❌ Failed to optimize {db_file}: {e}")

def create_backup():
    """Create backup of current database"""
    print("🔧 Creating Database Backup...")
    
    backup_dir = "database_backups"
    os.makedirs(backup_dir, exist_ok=True)
    
    db_files = ['toffice.db', 'instance/taluk_office.db']
    
    for db_file in db_files:
        if os.path.exists(db_file):
            try:
                backup_name = f"{backup_dir}/{os.path.basename(db_file)}.backup"
                shutil.copy2(db_file, backup_name)
                print(f"✅ Backed up {db_file} to {backup_name}")
            except Exception as e:
                print(f"❌ Failed to backup {db_file}: {e}")

def test_database_access():
    """Test database access after fixes"""
    print("🔧 Testing Database Access...")
    
    db_files = ['toffice.db', 'instance/taluk_office.db']
    
    for db_file in db_files:
        if os.path.exists(db_file):
            try:
                # Test connection
                conn = sqlite3.connect(db_file)
                cursor = conn.cursor()
                
                # Test read
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = cursor.fetchall()
                
                # Test write (create temporary table)
                cursor.execute("CREATE TEMPORARY TABLE test_access (id INTEGER)")
                cursor.execute("INSERT INTO test_access (id) VALUES (1)")
                cursor.execute("SELECT * FROM test_access")
                result = cursor.fetchone()
                
                conn.close()
                
                if result:
                    print(f"✅ Database access test passed for {db_file}")
                else:
                    print(f"❌ Database access test failed for {db_file}")
                    
            except Exception as e:
                print(f"❌ Database access test failed for {db_file}: {e}")

def fix_flask_app_database_config():
    """Fix Flask app database configuration issues"""
    print("🔧 Fixing Flask App Database Configuration...")
    
    try:
        # Update .env file for local development
        env_content = """SECRET_KEY=taluk-office-secret-key-2024-dev
# SQLite for local development (no authentication required)
# DATABASE_URL is not set, so it will use SQLite from config.py
FLASK_ENV=development
FLASK_DEBUG=1
"""
        
        with open('.env', 'w') as f:
            f.write(env_content)
        
        print("✅ Updated .env file for local development")
        
        # Ensure instance directory exists
        os.makedirs('instance', exist_ok=True)
        os.chmod('instance', 0o755)
        
        print("✅ Ensured instance directory exists with proper permissions")
        
    except Exception as e:
        print(f"❌ Failed to fix Flask configuration: {e}")

def main():
    """Main function to fix all database security issues"""
    print("🏛️  T-Office Database Security Fix Tool")
    print("=" * 60)
    print("Resolving database protection and permission issues...")
    print()
    
    # Step 1: Create backup
    create_backup()
    
    # Step 2: Remove locks
    remove_database_locks()
    
    # Step 3: Fix permissions
    ensure_database_permissions()
    
    # Step 4: Optimize database
    optimize_database()
    
    # Step 5: Fix Flask configuration
    fix_flask_app_database_config()
    
    # Step 6: Test access
    test_database_access()
    
    print("\n" + "=" * 60)
    print("🎉 Database Security Fix Complete!")
    print("=" * 60)
    print("✅ All database protection issues have been resolved")
    print("✅ Database permissions are properly configured")
    print("✅ No authentication barriers for local development")
    print("✅ Flask app should now run without database errors")
    print()
    print("🚀 You can now run: python app.py")

if __name__ == "__main__":
    main()
