// Enhanced Voice Search for T-Office
class VoiceSearch {
    constructor() {
        this.recognition = null;
        this.isListening = false;
        this.commands = {
            'search': this.handleSearchCommand.bind(this),
            'find': this.handleSearchCommand.bind(this),
            'open': this.handleOpenCommand.bind(this),
            'scan': this.handleScanCommand.bind(this),
            'add': this.handleAddCommand.bind(this),
            'create': this.handleAddCommand.bind(this),
            'dashboard': this.handleDashboardCommand.bind(this),
            'analytics': this.handleAnalyticsCommand.bind(this),
            'help': this.handleHelpCommand.bind(this)
        };
        this.init();
    }

    init() {
        if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            this.recognition = new SpeechRecognition();
            this.setupRecognition();
            this.setupEventListeners();
        } else {
            console.warn('Speech recognition not supported');
            this.hideVoiceButton();
        }
    }

    setupRecognition() {
        this.recognition.continuous = false;
        this.recognition.interimResults = false;
        this.recognition.lang = 'en-US';

        this.recognition.onstart = () => {
            this.isListening = true;
            this.showVoiceIndicator();
            this.updateVoiceButton(true);
        };

        this.recognition.onend = () => {
            this.isListening = false;
            this.hideVoiceIndicator();
            this.updateVoiceButton(false);
        };

        this.recognition.onresult = (event) => {
            const transcript = event.results[0][0].transcript.toLowerCase().trim();
            this.processVoiceCommand(transcript);
        };

        this.recognition.onerror = (event) => {
            console.error('Speech recognition error:', event.error);
            this.isListening = false;
            this.hideVoiceIndicator();
            this.updateVoiceButton(false);

            if (event.error === 'not-allowed') {
                this.showNotification('Microphone access denied', 'warning');
            } else {
                this.showNotification('Voice recognition error', 'danger');
            }
        };
    }

    setupEventListeners() {
        const voiceBtn = document.getElementById('voiceSearchBtn');
        if (voiceBtn) {
            voiceBtn.addEventListener('click', () => {
                this.toggleVoiceSearch();
            });
        }

        // Keyboard shortcut for voice search
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'm') {
                e.preventDefault();
                this.toggleVoiceSearch();
            }
        });
    }

    toggleVoiceSearch() {
        if (this.isListening) {
            this.stopListening();
        } else {
            this.startListening();
        }
    }

    startListening() {
        if (!this.recognition) {
            this.showNotification('Voice search not available', 'warning');
            return;
        }

        try {
            this.recognition.start();
            this.showNotification('Listening... Speak your command', 'info', 2000);
        } catch (error) {
            console.error('Error starting voice recognition:', error);
            this.showNotification('Could not start voice search', 'danger');
        }
    }

    stopListening() {
        if (this.recognition && this.isListening) {
            this.recognition.stop();
        }
    }

    processVoiceCommand(transcript) {
        console.log('Voice command:', transcript);

        // Find matching command
        const words = transcript.split(' ');
        const command = words[0];
        const params = words.slice(1).join(' ');

        if (this.commands[command]) {
            this.commands[command](params, transcript);
        } else {
            // Default to search if no specific command found
            this.handleSearchCommand(transcript, transcript);
        }
    }

    // Command handlers
    handleSearchCommand(query, fullTranscript) {
        if (!query.trim()) {
            this.showNotification('Please specify what to search for', 'warning');
            return;
        }

        const searchInput = document.getElementById('globalSearch');
        if (searchInput) {
            searchInput.value = query;
            searchInput.dispatchEvent(new Event('input'));
            searchInput.focus();
        }

        this.showNotification(`Searching for "${query}"`, 'info');
        this.trackVoiceCommand('search', { query });
    }

    handleOpenCommand(target, fullTranscript) {
        const routes = {
            'dashboard': '/dashboard',
            'files': '/dashboard',
            'analytics': '/analytics',
            'scan': '/scan',
            'scanner': '/scan',
            'collaboration': '/collaboration'
        };

        if (routes[target]) {
            window.location.href = routes[target];
            this.showNotification(`Opening ${target}`, 'success');
        } else {
            this.showNotification(`Don't know how to open "${target}"`, 'warning');
        }

        this.trackVoiceCommand('open', { target });
    }

    handleScanCommand(params, fullTranscript) {
        window.location.href = '/scan';
        this.showNotification('Opening QR scanner', 'success');
        this.trackVoiceCommand('scan');
    }

    handleAddCommand(params, fullTranscript) {
        window.location.href = '/add_file';
        this.showNotification('Opening add file page', 'success');
        this.trackVoiceCommand('add');
    }

    handleDashboardCommand(params, fullTranscript) {
        window.location.href = '/dashboard';
        this.showNotification('Opening dashboard', 'success');
        this.trackVoiceCommand('dashboard');
    }

    handleAnalyticsCommand(params, fullTranscript) {
        window.location.href = '/analytics';
        this.showNotification('Opening analytics', 'success');
        this.trackVoiceCommand('analytics');
    }

    handleHelpCommand(params, fullTranscript) {
        this.showVoiceHelp();
        this.trackVoiceCommand('help');
    }

    // UI methods
    showVoiceIndicator() {
        let indicator = document.getElementById('voiceIndicator');
        if (!indicator) {
            indicator = document.createElement('div');
            indicator.id = 'voiceIndicator';
            indicator.className = 'voice-indicator';
            indicator.innerHTML = '<i class="fas fa-microphone"></i>';
            document.body.appendChild(indicator);
        }
        indicator.style.display = 'flex';
    }

    hideVoiceIndicator() {
        const indicator = document.getElementById('voiceIndicator');
        if (indicator) {
            indicator.style.display = 'none';
        }
    }

    updateVoiceButton(isListening) {
        const voiceBtn = document.getElementById('voiceSearchBtn');
        if (voiceBtn) {
            if (isListening) {
                voiceBtn.classList.add('listening');
                voiceBtn.innerHTML = '<i class="fas fa-stop"></i>';
                voiceBtn.title = 'Stop listening';
            } else {
                voiceBtn.classList.remove('listening');
                voiceBtn.innerHTML = '<i class="fas fa-microphone"></i>';
                voiceBtn.title = 'Voice search (Ctrl+M)';
            }
        }
    }

    hideVoiceButton() {
        const voiceBtn = document.getElementById('voiceSearchBtn');
        if (voiceBtn) {
            voiceBtn.style.display = 'none';
        }
    }

    showNotification(message, type = 'info', duration = 3000) {
        if (window.TOffice && window.TOffice.showNotification) {
            window.TOffice.showNotification(message, type, duration);
        }
    }

    trackVoiceCommand(command, data = {}) {
        if (window.TOffice && window.TOffice.trackEvent) {
            window.TOffice.trackEvent('voice_command', { command, ...data });
        }
    }

    showVoiceHelp() {
        const helpModal = document.createElement('div');
        helpModal.className = 'modal fade';
        helpModal.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Voice Commands</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <h6>Available Commands:</h6>
                        <ul class="list-unstyled">
                            <li><strong>"Search [query]"</strong> - Search for files</li>
                            <li><strong>"Find [query]"</strong> - Search for files</li>
                            <li><strong>"Open dashboard"</strong> - Go to dashboard</li>
                            <li><strong>"Open analytics"</strong> - Go to analytics</li>
                            <li><strong>"Scan"</strong> - Open QR scanner</li>
                            <li><strong>"Add file"</strong> - Go to add file page</li>
                            <li><strong>"Help"</strong> - Show this help</li>
                        </ul>
                        <p class="text-muted">You can also use <kbd>Ctrl+M</kbd> to start voice search.</p>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(helpModal);
        const modal = new bootstrap.Modal(helpModal);
        modal.show();

        helpModal.addEventListener('hidden.bs.modal', () => {
            helpModal.remove();
        });
    }
}

// Initialize voice search when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.voiceSearch = new VoiceSearch();
});

// Add CSS for voice search
const voiceStyle = document.createElement('style');
voiceStyle.textContent = `
    .search-btn.listening {
        background: var(--danger-color) !important;
        animation: pulse 1s infinite;
    }

    @keyframes pulse {
        0% {
            box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
        }
        70% {
            box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
        }
        100% {
            box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
        }
    }
`;
document.head.appendChild(voiceStyle);